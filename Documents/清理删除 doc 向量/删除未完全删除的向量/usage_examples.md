# 孤立Chunk清理脚本使用示例

## 基本用法

### 1. 正常清理（按ID正序）
```bash
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml
```

### 2. 倒序清理（从最大ID开始）
```bash
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --reverse
# 或者使用短参数
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -r
```

### 3. 指定停止ID（倒序清理到指定ID停止）
```bash
# 从最大ID开始清理，遇到ID 1000时停止（不包含ID 1000）
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --reverse --stop-id 1000
# 或者使用短参数
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -r -s 1000
```

### 4. 指定停止ID（正序清理到指定ID停止）
```bash
# 从最小ID开始清理，遇到ID 1000时停止（不包含ID 1000）
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --stop-id 1000
# 或者使用短参数
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -s 1000
```

## 参数说明

- `config_path`: 配置文件路径（必需参数）
- `--reverse` / `-r`: 按ID倒序处理（从最大ID开始）
- `--stop-id` / `-s`: 停止处理的ID，当遇到此ID时停止（不包含此ID）

## 使用场景

### 场景1：测试清理功能
```bash
# 先从最大的几个ID开始测试，遇到ID 9990时停止
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -r -s 9990
```

### 场景2：分批清理大量数据
```bash
# 第一批：从最大ID清理到ID 5000
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -r -s 5000

# 第二批：从ID 5000清理到ID 1000
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -r -s 1000

# 第三批：清理剩余的（ID < 1000）
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -r
```

### 场景3：只清理特定范围的数据
```bash
# 只清理ID大于1000的数据（倒序清理，遇到ID 1000停止）
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml -r -s 1000
```

## 注意事项

1. **停止ID不包含在处理范围内**：当指定 `--stop-id 1000` 时，ID为1000的记录不会被处理
2. **倒序处理更安全**：建议使用 `--reverse` 参数从最新的数据开始清理，便于测试和控制
3. **分批处理**：对于大量数据，建议分批处理以避免长时间运行
4. **日志记录**：所有操作都会记录在 `cleanup_orphaned_chunks.log` 文件中

## 查看帮助
```bash
python cleanup_orphaned_chunks.py --help
```
