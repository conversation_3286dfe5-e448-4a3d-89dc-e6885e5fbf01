# RAG服务孤立chunk数据清理脚本

该脚本用于清理t_doc表中已软删除的QA类型文档在RAG服务中的孤立chunk数据。

## 功能特性

- 查询t_doc表中已软删除的QA类型文档
- 遍历所有助手，获取对应的collection信息
- 调用RAG服务的get_collection接口获取chunk_id
- 如果存在chunk_id，调用delete_vec接口删除向量数据
- 支持多种处理模式：全量、倒序、停止ID、ID范围

## 使用方法

### 基本用法

```bash
# 全量处理所有已软删除的QA文档
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml

# 倒序处理（从最大ID开始）
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --reverse

# 处理到指定ID时停止（不包含停止ID）
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --stop-id 1000

# 倒序处理并在指定ID停止
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --reverse --stop-id 2000
```

### ID范围处理（新功能）

```bash
# 处理ID范围 100-500 的文档
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --start-id 100 --end-id 500

# 从ID 1000开始处理到最后
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --start-id 1000

# 处理到ID 500为止
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --end-id 500

# 倒序处理ID范围 200-800
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --reverse --start-id 200 --end-id 800

# 组合使用：处理ID范围 100-1000，倒序，遇到ID 500停止
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --reverse --start-id 100 --end-id 1000 --stop-id 500
```

## 参数说明

| 参数 | 简写 | 类型 | 说明 |
|------|------|------|------|
| `config_path` | - | 必需 | 配置文件路径（例如：../bin/etc/ai.toml） |
| `--reverse` | `-r` | 可选 | 按ID倒序处理（从最大ID开始） |
| `--stop-id` | `-s` | 可选 | 停止处理的ID，当遇到此ID时停止（不包含此ID） |
| `--start-id` | - | 可选 | 起始ID（包含），指定处理范围的开始 |
| `--end-id` | - | 可选 | 结束ID（包含），指定处理范围的结束 |

## 使用场景

### 1. 全量清理
适用于首次运行或需要清理所有孤立数据的场景。

### 2. 增量清理
使用 `--start-id` 从上次处理的位置继续。

### 3. 分批处理
使用 `--start-id` 和 `--end-id` 分批处理大量数据，避免长时间运行。

### 4. 测试验证
使用小范围ID测试脚本功能：
```bash
python cleanup_orphaned_chunks.py ../bin/etc/ai.toml --start-id 100 --end-id 110
```

### 5. 紧急停止
如果需要在特定ID停止处理，使用 `--stop-id`。

## 日志输出

脚本会输出详细的处理日志，包括：
- 处理的文档数量和ID范围
- 每个文档在各个Collection中找到的chunk数量
- 删除操作的成功/失败状态
- 最终的统计信息

日志同时输出到控制台和 `cleanup_orphaned_chunks.log` 文件。

## 注意事项

1. 确保配置文件路径正确
2. 起始ID不能大于结束ID
3. 建议先用小范围测试
4. 可以随时使用 Ctrl+C 中断执行
5. 删除操作不可逆，请谨慎使用

## 配置文件

脚本需要 `ai.toml` 配置文件，包含数据库连接和RAG服务配置信息。
