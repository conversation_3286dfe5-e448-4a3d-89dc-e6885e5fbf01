#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG服务孤立chunk数据清理脚本

该脚本用于清理t_doc表中已软删除的QA类型文档在RAG服务中的孤立chunk数据。
由于之前代码bug导致t_doc删除时，RAG服务内的向量数据没有完全删除，需要进行全量数据库清洗。

功能：
1. 查询t_doc表中已软删除的QA类型文档
2. 遍历所有助手，获取对应的collection信息
3. 调用RAG服务的get_collection接口获取chunk_id
4. 如果存在chunk_id，调用delete_vec接口删除向量数据

支持的处理模式：
- 全量处理：处理所有已软删除的QA文档
- 倒序处理：从最大ID开始处理（--reverse）
- 停止ID：处理到指定ID时停止（--stop-id）
- ID范围：指定起始和结束ID范围（--start-id, --end-id）

配置文件：ai.toml
"""

import sys
import os
import logging
import json
import requests
import pymysql
import toml
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cleanup_orphaned_chunks.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str
    port: int
    user: str
    password: str
    database: str


@dataclass
class RagConfig:
    """RAG服务配置"""
    host: str
    schema: str = "http"
    get_collection_path: str = "/collection/get"
    delete_vec_path: str = "/collection/delete_chunks_byid"


@dataclass
class OrphanedDoc:
    """孤立文档信息"""
    doc_id: int
    file_name: str
    deleted_at: str


@dataclass
class AssistantCollection:
    """助手Collection信息"""
    assistant_id: int
    assistant_name: str
    collection_id: int
    collection_name: str
    rag_name: str
    vdb_type: str
    es_ins: str


class ConfigLoader:
    """配置文件加载器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return toml.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        db_config = self.config['db']['connections']['v2']
        return DatabaseConfig(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['dbname']
        )
    
    def get_rag_config(self) -> RagConfig:
        """获取RAG服务配置"""
        llm_config = self.config['llm']
        paths = llm_config.get('paths', {})
        
        return RagConfig(
            host=llm_config['host2'],
            schema=llm_config.get('schema', 'http'),
            get_collection_path=paths.get('getCollection', '/collection/get'),
            delete_vec_path=paths.get('deleteVec', '/collection/delete_chunks_byid')
        )


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connection = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                database=self.config.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def get_orphaned_qa_docs(self, reverse_order: bool = False, stop_id: Optional[int] = None,
                           start_id: Optional[int] = None, end_id: Optional[int] = None) -> List[OrphanedDoc]:
        """获取已软删除的QA类型文档

        Args:
            reverse_order: 是否按ID倒序排列（从最大ID开始）
            stop_id: 停止处理的ID，当遇到此ID时停止（不包含此ID）
            start_id: 起始ID（包含），指定处理范围的开始
            end_id: 结束ID（包含），指定处理范围的结束
        """
        order_clause = "ORDER BY id DESC" if reverse_order else "ORDER BY id"

        # 构建WHERE条件
        where_conditions = ["data_type = 1", "deleted_at IS NOT NULL"]

        # 添加ID范围条件
        if start_id is not None:
            where_conditions.append(f"id >= {start_id}")
        if end_id is not None:
            where_conditions.append(f"id <= {end_id}")

        where_clause = " AND ".join(where_conditions)

        query = f"""
        SELECT id, rag_filename as file_name, deleted_at
        FROM t_doc
        WHERE {where_clause}
        {order_clause}
        """

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query)
                results = cursor.fetchall()

                docs = []
                for row in results:
                    doc_id = row['id']

                    # 如果指定了停止ID，当遇到此ID时停止处理
                    if stop_id is not None and doc_id == stop_id:
                        logger.info(f"遇到停止ID {stop_id}，停止处理")
                        break

                    docs.append(OrphanedDoc(
                        doc_id=doc_id,
                        file_name=row['file_name'] or '',
                        deleted_at=str(row['deleted_at'])
                    ))

                order_desc = "倒序" if reverse_order else "正序"
                range_desc = ""
                if start_id is not None or end_id is not None:
                    range_parts = []
                    if start_id is not None:
                        range_parts.append(f"起始ID: {start_id}")
                    if end_id is not None:
                        range_parts.append(f"结束ID: {end_id}")
                    range_desc = f"，范围: {', '.join(range_parts)}"

                stop_desc = f"，停止ID: {stop_id}" if stop_id is not None else ""
                logger.info(f"找到 {len(docs)} 个已软删除的QA文档（{order_desc}{range_desc}{stop_desc}）")
                return docs

        except Exception as e:
            logger.error(f"查询孤立QA文档失败: {e}")
            raise
    
    def get_all_assistant_collections(self) -> List[AssistantCollection]:
        """获取所有助手的Collection信息"""
        query = """
        SELECT 
            a.id as assistant_id,
            a.name as assistant_name,
            c.id as collection_id,
            c.name as collection_name,
            c.rag_name,
            c.vdb_type,
            c.es_ins
        FROM t_assistant a
        JOIN t_assistant_collection ac ON a.id = ac.assistant_id
        JOIN t_collection c ON ac.collection_id = c.id
        WHERE a.deleted_at IS NULL
        ORDER BY a.id, c.id
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query)
                results = cursor.fetchall()
                
                collections = []
                for row in results:
                    collections.append(AssistantCollection(
                        assistant_id=row['assistant_id'],
                        assistant_name=row['assistant_name'],
                        collection_id=row['collection_id'],
                        collection_name=row['collection_name'],
                        rag_name=row['rag_name'],
                        vdb_type=row['vdb_type'] or '',
                        es_ins=row['es_ins'] or ''
                    ))
                
                logger.info(f"找到 {len(collections)} 个助手Collection关系")
                return collections
                
        except Exception as e:
            logger.error(f"查询助手Collection信息失败: {e}")
            raise


class RagClient:
    """RAG服务客户端"""
    
    def __init__(self, config: RagConfig):
        self.config = config
        self.base_url = f"{config.schema}://{config.host}"
        self.session = requests.Session()
        self.session.timeout = 30
    
    def get_collection_chunks(self, collection_name: str, file_name: str,
                            vdb_type: str = "", es_ins: str = "") -> List[str]:
        """获取collection中指定文档的chunk IDs"""
        url = f"{self.base_url}{self.config.get_collection_path}"

        payload = {
            "collection_name": collection_name,
            "file_name": file_name
        }

        if vdb_type:
            payload["vdb_type"] = vdb_type
        if es_ins:
            payload["es_ins"] = es_ins

        # 打印请求日志
        # logger.info(f"发送get_collection请求:")
        # logger.info(f"  URL: {url}")
        # logger.info(f"  Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")

        try:
            response = self.session.post(url, json=payload)

            # 打印响应状态日志
            # logger.info(f"收到get_collection响应:")
            # logger.info(f"  状态码: {response.status_code}")
            # logger.info(f"  响应头: {dict(response.headers)}")

            response.raise_for_status()

            result = response.json()

            # 打印响应内容日志
            # logger.info(f"  响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get('code') == 10000 and 'info' in result:
                chunk_ids = result['info'].get('ids', [])
                if len(chunk_ids) > 0:
                    logger.info(f"Collection {collection_name} 文档 {file_name} 找到 {len(chunk_ids)} 个chunks: {chunk_ids}")
                return chunk_ids
            else:
                if result.get('code') != 10000:
                    cont = json.dumps(result, ensure_ascii=False, indent=2)
                    if "index_not_found_exception" not in cont:
                        logger.warning(f"Collection {collection_name} 文档 {file_name} 响应异常")
                        logger.info(f"  响应内容: {cont}")
                return []

        except requests.exceptions.RequestException as e:
            logger.error(f"获取collection chunks网络请求失败 {collection_name}/{file_name}: {e}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"解析collection响应JSON失败 {collection_name}/{file_name}: {e}")
            logger.error(f"原始响应内容: {response.text if 'response' in locals() else 'N/A'}")
            return []
        except Exception as e:
            logger.error(f"获取collection chunks未知异常 {collection_name}/{file_name}: {e}")
            return []
    
    def delete_vec_chunks(self, collection_name: str, chunk_ids: List[str],
                         vdb_type: str = "", es_ins: str = "") -> bool:
        """删除指定的向量chunks"""
        if not chunk_ids:
            logger.info(f"Collection {collection_name} 没有需要删除的chunks，跳过")
            return True

        url = f"{self.base_url}{self.config.delete_vec_path}"

        payload = {
            "collection_name": collection_name,
            "vec_ids": chunk_ids
        }

        if vdb_type:
            payload["vdb_type"] = vdb_type
        if es_ins:
            payload["es_ins"] = es_ins

        # 打印请求日志
        # logger.info(f"发送delete_vec请求:")
        # logger.info(f"  URL: {url}")
        # logger.info(f"  Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")

        try:
            response = self.session.post(url, json=payload)

            # 打印响应状态日志
            # logger.info(f"收到delete_vec响应:")
            # logger.info(f"  状态码: {response.status_code}")
            # logger.info(f"  响应头: {dict(response.headers)}")

            response.raise_for_status()

            result = response.json()

            # 打印响应内容日志
            # logger.info(f"  响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get('code') == 10000:
                logger.info(f"✅ 成功删除 Collection {collection_name} 中的 {len(chunk_ids)} 个chunks")
                return True
            else:
                logger.error(f"❌ 删除chunks失败 {collection_name}, 响应码: {result.get('code')}, 消息: {result.get('message', 'N/A')}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 删除向量chunks网络请求失败 {collection_name}: {e}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"❌ 解析delete_vec响应JSON失败 {collection_name}: {e}")
            logger.error(f"原始响应内容: {response.text if 'response' in locals() else 'N/A'}")
            return False
        except Exception as e:
            logger.error(f"❌ 删除向量chunks未知异常 {collection_name}: {e}")
            return False


class OrphanedChunkCleaner:
    """孤立chunk清理器"""
    
    def __init__(self, config_path: str):
        self.config_loader = ConfigLoader(config_path)
        self.db_manager = DatabaseManager(self.config_loader.get_database_config())
        self.rag_client = RagClient(self.config_loader.get_rag_config())

        # 统计信息
        self.stats = {
            'total_orphaned_docs': 0,
            'total_collections': 0,
            'total_chunks_found': 0,
            'total_chunks_deleted': 0,
            'failed_deletions': 0
        }
    
    def run(self, reverse_order: bool = False, stop_id: Optional[int] = None,
            start_id: Optional[int] = None, end_id: Optional[int] = None):
        """执行清理任务

        Args:
            reverse_order: 是否按ID倒序处理（从最大ID开始）
            stop_id: 停止处理的ID，当遇到此ID时停止（不包含此ID）
            start_id: 起始ID（包含），指定处理范围的开始
            end_id: 结束ID（包含），指定处理范围的结束
        """
        order_desc = "倒序" if reverse_order else "正序"

        # 构建描述信息
        desc_parts = [order_desc]
        if start_id is not None or end_id is not None:
            range_parts = []
            if start_id is not None:
                range_parts.append(f"起始ID: {start_id}")
            if end_id is not None:
                range_parts.append(f"结束ID: {end_id}")
            desc_parts.append(f"范围: {', '.join(range_parts)}")
        if stop_id is not None:
            desc_parts.append(f"停止ID: {stop_id}")

        desc = "，".join(desc_parts)
        logger.info(f"开始执行孤立chunk清理任务（{desc}）")
        start_time = datetime.now()

        try:
            # 连接数据库
            self.db_manager.connect()

            # 获取孤立文档
            orphaned_docs = self.db_manager.get_orphaned_qa_docs(reverse_order, stop_id, start_id, end_id)
            self.stats['total_orphaned_docs'] = len(orphaned_docs)

            if not orphaned_docs:
                logger.info("没有找到需要清理的孤立QA文档")
                return

            # 获取所有助手Collection信息
            assistant_collections = self.db_manager.get_all_assistant_collections()
            self.stats['total_collections'] = len(assistant_collections)

            if not assistant_collections:
                logger.warning("没有找到任何助手Collection信息")
                return

            # 执行清理
            self._cleanup_orphaned_chunks(orphaned_docs, assistant_collections)

        except Exception as e:
            logger.error(f"清理任务执行失败: {e}")
            raise
        finally:
            self.db_manager.close()

        # 输出统计信息
        end_time = datetime.now()
        duration = end_time - start_time
        self._print_stats(duration)
    
    def _cleanup_orphaned_chunks(self, orphaned_docs: List[OrphanedDoc], 
                                assistant_collections: List[AssistantCollection]):
        """清理孤立chunks"""
        logger.info(f"开始清理 {len(orphaned_docs)} 个孤立文档在 {len(assistant_collections)} 个Collection中的chunks")
        
        for doc in orphaned_docs:
            logger.info(f"处理孤立文档 ID:{doc.doc_id}, 文件名:{doc.file_name}")
            
            for collection in assistant_collections:
                self._process_doc_in_collection(doc, collection)
    
    def _process_doc_in_collection(self, doc: OrphanedDoc, collection: AssistantCollection):
        """处理单个文档在指定Collection中的chunks"""
        # 获取chunks
        chunk_ids = self.rag_client.get_collection_chunks(
            collection_name=collection.rag_name,
            file_name=doc.file_name,
            vdb_type=collection.vdb_type,
            es_ins=collection.es_ins
        )
        
        if not chunk_ids:
            return
        
        self.stats['total_chunks_found'] += len(chunk_ids)
        logger.info(f"文档 {doc.doc_id} 在 Collection {collection.rag_name} 中找到 {len(chunk_ids)} 个chunks")
        
        # 删除chunks
        success = self.rag_client.delete_vec_chunks(
            collection_name=collection.rag_name,
            chunk_ids=chunk_ids,
            vdb_type=collection.vdb_type,
            es_ins=collection.es_ins
        )
        
        if success:
            self.stats['total_chunks_deleted'] += len(chunk_ids)
        else:
            self.stats['failed_deletions'] += len(chunk_ids)
    
    def _print_stats(self, duration):
        """打印统计信息"""
        logger.info("=" * 50)
        logger.info("清理任务完成统计:")
        logger.info(f"执行时间: {duration}")
        logger.info(f"孤立文档总数: {self.stats['total_orphaned_docs']}")
        logger.info(f"Collection总数: {self.stats['total_collections']}")
        logger.info(f"发现chunks总数: {self.stats['total_chunks_found']}")
        logger.info(f"成功删除chunks: {self.stats['total_chunks_deleted']}")
        logger.info(f"删除失败chunks: {self.stats['failed_deletions']}")
        logger.info("=" * 50)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='RAG服务孤立chunk数据清理脚本')
    parser.add_argument('config_path', help='配置文件路径 (例如: ../bin/etc/ai.toml)')
    parser.add_argument('--reverse', '-r', action='store_true',
                       help='按ID倒序处理（从最大ID开始）')
    parser.add_argument('--stop-id', '-s', type=int,
                       help='停止处理的ID，当遇到此ID时停止（不包含此ID）')
    parser.add_argument('--start-id', type=int,
                       help='起始ID（包含），指定处理范围的开始')
    parser.add_argument('--end-id', type=int,
                       help='结束ID（包含），指定处理范围的结束')

    args = parser.parse_args()

    # 验证参数
    if args.start_id is not None and args.end_id is not None and args.start_id > args.end_id:
        print("错误: 起始ID不能大于结束ID")
        sys.exit(1)

    if not os.path.exists(args.config_path):
        print(f"配置文件不存在: {args.config_path}")
        sys.exit(1)

    try:
        cleaner = OrphanedChunkCleaner(args.config_path)
        cleaner.run(reverse_order=args.reverse, stop_id=args.stop_id,
                   start_id=args.start_id, end_id=args.end_id)
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
