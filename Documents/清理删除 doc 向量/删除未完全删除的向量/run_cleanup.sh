#!/bin/bash

# RAG孤立chunk清理脚本执行器
# 使用方法: ./run_cleanup.sh

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_PATH="$SCRIPT_DIR/etc/ai.toml"

echo "RAG孤立Chunk清理脚本"
echo "===================="
echo "脚本目录: $SCRIPT_DIR"
echo "配置文件: $CONFIG_PATH"
echo ""

# 检查配置文件是否存在
if [ ! -f "$CONFIG_PATH" ]; then
    echo "错误: 配置文件不存在: $CONFIG_PATH"
    exit 1
fi

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3命令"
    exit 1
fi

# 检查依赖是否安装
echo "检查Python依赖..."
if ! python3 -c "import requests, pymysql, toml" 2>/dev/null; then
    echo "安装依赖包..."
    pip3 install -r "$SCRIPT_DIR/requirements.txt" -i https://mirrors.tencent.com/pypi/simple/
fi

# 确认执行
echo "即将开始清理RAG服务中的孤立chunk数据"
echo "这将处理所有已软删除的QA类型文档"
echo ""
# read -p "确认继续执行? (y/N): " -n 1 -r
# echo ""

# if [[ ! $REPLY =~ ^[Yy]$ ]]; then
#     echo "取消执行"
#     exit 0
# fi

echo ""
echo "开始执行清理任务..."
echo "===================="

# 执行清理脚本
cd "$SCRIPT_DIR"
python3 cleanup_orphaned_chunks.py "$CONFIG_PATH"

echo ""
echo "清理任务完成!"
echo "详细日志请查看: $SCRIPT_DIR/cleanup_orphaned_chunks.log"
